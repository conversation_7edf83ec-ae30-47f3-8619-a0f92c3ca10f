"""
Metadata Export Service for generating Markdown and PDF exports of dataset metadata.
"""

import os
import json
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional
from flask import current_app
from app.models.dataset import Dataset
from app.models.metadata import MetadataQuality

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False


class MetadataExportService:
    """Service for exporting dataset metadata to various formats"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        self.export_dir = os.path.join(current_app.root_path, 'static', 'exports')
        os.makedirs(self.export_dir, exist_ok=True)
    
    def export_metadata_markdown(self, dataset_id: str) -> Optional[str]:
        """
        Export dataset metadata as a Markdown file.
        
        Args:
            dataset_id: ID of the dataset
            
        Returns:
            Path to the generated Markdown file or None if failed
        """
        try:
            dataset = Dataset.find_by_id(dataset_id)
            if not dataset:
                return None
            
            metadata_quality = MetadataQuality.get_by_dataset(dataset_id)
            
            # Generate Markdown content
            markdown_content = self._generate_markdown_content(dataset, metadata_quality)
            
            # Save to file
            filename = f"{dataset.title.replace(' ', '_')}_metadata_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            file_path = os.path.join(self.export_dir, filename)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            return file_path
            
        except Exception as e:
            print(f"Error exporting metadata to Markdown: {e}")
            return None
    
    def export_metadata_pdf(self, dataset_id: str) -> Optional[str]:
        """
        Export dataset metadata as a PDF file.
        
        Args:
            dataset_id: ID of the dataset
            
        Returns:
            Path to the generated PDF file or None if failed
        """
        if not REPORTLAB_AVAILABLE:
            print("ReportLab not available for PDF generation")
            return None
        
        try:
            dataset = Dataset.find_by_id(dataset_id)
            if not dataset:
                return None
            
            metadata_quality = MetadataQuality.get_by_dataset(dataset_id)
            
            # Generate PDF
            filename = f"{dataset.title.replace(' ', '_')}_metadata_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = os.path.join(self.export_dir, filename)
            
            self._generate_pdf_content(dataset, metadata_quality, file_path)
            
            return file_path
            
        except Exception as e:
            print(f"Error exporting metadata to PDF: {e}")
            return None
    
    def _generate_markdown_content(self, dataset: Dataset, metadata_quality: Optional[MetadataQuality]) -> str:
        """Generate Markdown content for dataset metadata"""
        
        content = f"""# {dataset.title}

## Dataset Metadata Report

**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Dataset ID:** {dataset.id}  
**Export Type:** Complete Metadata Export

---

## 📊 Basic Information

| Field | Value |
|-------|-------|
| **Title** | {dataset.title} |
| **Source** | {dataset.source or 'Not specified'} |
| **Category** | {dataset.category or 'Not specified'} |
| **Data Type** | {dataset.data_type or 'Not specified'} |
| **Format** | {dataset.format or 'Unknown'} |
| **File Size** | {dataset.file_size or 'Unknown'} |
| **License** | {dataset.license or 'Not specified'} |
| **Created** | {dataset.created_at.strftime('%Y-%m-%d %H:%M:%S') if dataset.created_at else 'Unknown'} |
| **Updated** | {dataset.updated_at.strftime('%Y-%m-%d %H:%M:%S') if dataset.updated_at else 'Unknown'} |

## 📝 Description

{dataset.description or 'No description available.'}

## 📈 Data Statistics

| Metric | Value |
|--------|-------|
| **Records** | {dataset.record_count or 'Unknown'} |
| **Fields** | {dataset.field_count or 'Unknown'} |
| **Status** | {dataset.status or 'Unknown'} |

"""

        # Add field information if available
        if dataset.field_names:
            content += "## 🏷️ Field Names\n\n"
            fields = dataset.field_names.split(',')
            for i, field in enumerate(fields[:20], 1):  # Limit to first 20 fields
                content += f"{i}. {field.strip()}\n"
            if len(fields) > 20:
                content += f"\n*... and {len(fields) - 20} more fields*\n"
            content += "\n"

        # Add data types if available
        if dataset.data_types:
            content += "## 🔢 Data Types\n\n"
            data_types = dataset.data_types.split(',')
            for dtype in data_types[:10]:  # Limit to first 10 types
                content += f"- {dtype.strip()}\n"
            content += "\n"

        # Add tags if available
        if dataset.tags:
            content += "## 🏷️ Tags\n\n"
            tags = dataset.tags.split(',')
            for tag in tags[:15]:  # Limit to first 15 tags
                content += f"- #{tag.strip()}\n"
            content += "\n"

        # Add keywords if available
        if dataset.keywords:
            content += "## 🔍 Keywords\n\n"
            keywords = dataset.keywords.split(',')
            for keyword in keywords[:15]:  # Limit to first 15 keywords
                content += f"- {keyword.strip()}\n"
            content += "\n"

        # Add use cases if available
        if dataset.use_cases:
            content += "## 💡 Use Cases\n\n"
            use_cases = dataset.use_cases.split(',')
            for use_case in use_cases[:10]:  # Limit to first 10 use cases
                content += f"- {use_case.strip()}\n"
            content += "\n"

        # Add quality metrics if available
        if metadata_quality:
            content += f"""## 📊 Quality Assessment

| Metric | Score |
|--------|-------|
| **Overall Quality** | {metadata_quality.quality_score or 0}% |
| **Completeness** | {metadata_quality.completeness or 0}% |
| **Consistency** | {metadata_quality.consistency or 0}% |
| **Accuracy** | {metadata_quality.accuracy or 'N/A'}% |

"""

        # Add FAIR compliance
        content += f"""## 🎯 FAIR Compliance

| Principle | Score | Status |
|-----------|-------|--------|
| **FAIR Score** | {dataset.fair_score or 0}% | {'✅ Compliant' if dataset.fair_compliant else '⚠️ Partial'} |
| **Findable** | {getattr(dataset, 'findable_score', 'N/A')} | {'✅' if getattr(dataset, 'findable_score', 0) >= 75 else '⚠️'} |
| **Accessible** | {getattr(dataset, 'accessible_score', 'N/A')} | {'✅' if getattr(dataset, 'accessible_score', 0) >= 75 else '⚠️'} |
| **Interoperable** | {getattr(dataset, 'interoperable_score', 'N/A')} | {'✅' if getattr(dataset, 'interoperable_score', 0) >= 75 else '⚠️'} |
| **Reusable** | {getattr(dataset, 'reusable_score', 'N/A')} | {'✅' if getattr(dataset, 'reusable_score', 0) >= 75 else '⚠️'} |

"""

        # Add compliance information
        content += f"""## ✅ Standards Compliance

- **Schema.org Compliant:** {'✅ Yes' if getattr(dataset, 'schema_org_compliant', False) else '❌ No'}
- **Persistent Identifier:** {'✅ Assigned' if dataset.persistent_identifier else '❌ Not assigned'}
- **Encoding Format:** {dataset.encoding_format or 'Not specified'}

"""

        # Add metadata standards
        if dataset.dublin_core or dataset.dcat_metadata or dataset.json_ld:
            content += "## 📋 Available Metadata Standards\n\n"
            if dataset.dublin_core:
                content += "- ✅ Dublin Core\n"
            if dataset.dcat_metadata:
                content += "- ✅ DCAT (Data Catalog Vocabulary)\n"
            if dataset.json_ld:
                content += "- ✅ Schema.org JSON-LD\n"
            content += "\n"

        # Add footer
        content += f"""---

## 📄 Export Information

- **Export Format:** Markdown
- **Generated by:** AI-Powered Metadata Harvesting System
- **Export Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Dataset URL:** {dataset.source_url or 'N/A'}

*This metadata export contains comprehensive information about the dataset including quality metrics, FAIR compliance assessment, and standards compliance.*
"""

        return content
    
    def _generate_pdf_content(self, dataset: Dataset, metadata_quality: Optional[MetadataQuality], file_path: str):
        """Generate PDF content for dataset metadata"""
        
        doc = SimpleDocTemplate(file_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            textColor=colors.darkblue
        )
        story.append(Paragraph(f"Dataset Metadata Report: {dataset.title}", title_style))
        story.append(Spacer(1, 12))
        
        # Basic information table
        basic_info_data = [
            ['Field', 'Value'],
            ['Title', dataset.title],
            ['Source', dataset.source or 'Not specified'],
            ['Category', dataset.category or 'Not specified'],
            ['Format', dataset.format or 'Unknown'],
            ['Records', str(dataset.record_count or 'Unknown')],
            ['Fields', str(dataset.field_count or 'Unknown')],
            ['Created', dataset.created_at.strftime('%Y-%m-%d') if dataset.created_at else 'Unknown']
        ]
        
        basic_info_table = Table(basic_info_data, colWidths=[2*inch, 4*inch])
        basic_info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(Paragraph("Basic Information", styles['Heading2']))
        story.append(basic_info_table)
        story.append(Spacer(1, 12))
        
        # Description
        if dataset.description:
            story.append(Paragraph("Description", styles['Heading2']))
            story.append(Paragraph(dataset.description, styles['Normal']))
            story.append(Spacer(1, 12))
        
        # Quality metrics if available
        if metadata_quality:
            story.append(Paragraph("Quality Assessment", styles['Heading2']))
            quality_data = [
                ['Metric', 'Score'],
                ['Overall Quality', f"{metadata_quality.quality_score or 0}%"],
                ['Completeness', f"{metadata_quality.completeness or 0}%"],
                ['Consistency', f"{metadata_quality.consistency or 0}%"],
                ['FAIR Score', f"{dataset.fair_score or 0}%"]
            ]
            
            quality_table = Table(quality_data, colWidths=[3*inch, 2*inch])
            quality_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(quality_table)
            story.append(Spacer(1, 12))
        
        # Footer
        story.append(Spacer(1, 24))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            textColor=colors.grey
        )
        story.append(Paragraph(f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by Metadata Generation System To Support AI Research Initiative", footer_style))
        
        # Build PDF
        doc.build(story)


def get_metadata_export_service():
    """Get metadata export service instance"""
    return MetadataExportService()
