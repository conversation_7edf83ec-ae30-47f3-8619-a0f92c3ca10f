"""
NLP Service for dataset processing and analysis.
"""

import re
import json
import os
from collections import Counter
from datetime import datetime
from typing import List, Dict, Any, Optional

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.stem import PorterStemmer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    import numpy as np
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    from transformers import AutoTokenizer, AutoModel, pipeline
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

# Removed paid APIs - using only free AI models

try:
    from mistralai import Mistral
    MISTRAL_AVAILABLE = True
except ImportError:
    MISTRAL_AVAILABLE = False

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False

try:
    import requests
    HF_AVAILABLE = True
except ImportError:
    HF_AVAILABLE = False

try:
    import together
    TOGETHER_AVAILABLE = True
except ImportError:
    TOGETHER_AVAILABLE = False


class NLPService:
    """Service for NLP processing of dataset content and metadata"""
    
    def __init__(self):
        self.nlp = None
        self.stemmer = None
        self.stop_words = set()
        self.tfidf_vectorizer = None
        self.bert_tokenizer = None
        self.bert_model = None
        self.ner_pipeline = None

        # Free AI model clients (only using free APIs)
        self.mistral_client = None
        self.groq_client = None
        self.together_client = None
        self.hf_api_key = None

        self._initialize_nlp()
        self._initialize_advanced_models()
    
    def _initialize_nlp(self):
        """Initialize advanced NLP libraries if available"""
        print("🔧 Initializing Advanced NLP Service with BERT, TF-IDF, and NER...")

        # Initialize spaCy if available - try larger models first
        if SPACY_AVAILABLE:
            try:
                # Try to load the transformer-based model first (most accurate)
                self.nlp = spacy.load("en_core_web_trf")
                print("✅ spaCy Transformer model (en_core_web_trf) loaded successfully")
            except OSError:
                try:
                    # Fallback to large model
                    self.nlp = spacy.load("en_core_web_lg")
                    print("✅ spaCy Large model (en_core_web_lg) loaded successfully")
                except OSError:
                    try:
                        # Fallback to medium model
                        self.nlp = spacy.load("en_core_web_md")
                        print("✅ spaCy Medium model (en_core_web_md) loaded successfully")
                    except OSError:
                        try:
                            # Final fallback to small model
                            self.nlp = spacy.load("en_core_web_sm")
                            print("✅ spaCy Small model (en_core_web_sm) loaded successfully")
                        except OSError:
                            print("⚠️ No spaCy English model found. Install with:")
                            print("   python -m spacy download en_core_web_trf  # Best (transformer-based)")
                            print("   python -m spacy download en_core_web_lg   # Large")
                            print("   python -m spacy download en_core_web_md   # Medium")
                            print("   python -m spacy download en_core_web_sm   # Small")
                            self.nlp = None

        # Initialize NLTK if available
        if NLTK_AVAILABLE:
            try:
                self.stemmer = PorterStemmer()
                self.stop_words = set(stopwords.words('english'))
                print("✅ NLTK initialized successfully")
            except LookupError:
                print("⚠️ NLTK data not found. Download with: nltk.download('stopwords') and nltk.download('punkt')")
                self.stemmer = None
                self.stop_words = set()

        # Initialize TF-IDF Vectorizer
        if SKLEARN_AVAILABLE:
            try:
                self.tfidf_vectorizer = TfidfVectorizer(
                    max_features=500,
                    stop_words='english',
                    ngram_range=(1, 2),
                    min_df=1,  # Minimum document frequency
                    max_df=0.99,  # Maximum document frequency
                    lowercase=True,
                    token_pattern=r'\b[a-zA-Z]{2,}\b'  # Only alphabetic tokens with 2+ chars
                )
                print("✅ TF-IDF Vectorizer initialized successfully")
            except Exception as e:
                print(f"⚠️ TF-IDF initialization failed: {e}")
                self.tfidf_vectorizer = None

        # Initialize BERT model for embeddings - try better models first
        if TRANSFORMERS_AVAILABLE:
            try:
                # Try RoBERTa-large first (most robust)
                try:
                    model_name = "roberta-large"
                    self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name)
                    self.bert_model = AutoModel.from_pretrained(model_name)
                    print("✅ RoBERTa-Large model loaded successfully")
                except Exception as e:
                    print(f"⚠️ RoBERTa-Large failed: {e}, trying BERT-large...")
                    try:
                        # Fallback to BERT-large
                        model_name = "bert-large-uncased"
                        self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name)
                        self.bert_model = AutoModel.from_pretrained(model_name)
                        print("✅ BERT-Large model loaded successfully")
                    except Exception as e:
                        print(f"⚠️ BERT-Large failed: {e}, trying BERT-base...")
                        try:
                            # Fallback to BERT-base
                            model_name = "bert-base-uncased"
                            self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name)
                            self.bert_model = AutoModel.from_pretrained(model_name)
                            print("✅ BERT-Base model loaded successfully")
                        except Exception as e:
                            print(f"⚠️ BERT-Base failed: {e}, trying DistilBERT...")
                            # Final fallback to DistilBERT
                            model_name = "distilbert-base-uncased"
                            self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name)
                            self.bert_model = AutoModel.from_pretrained(model_name)
                            print("✅ DistilBERT model loaded successfully")

                # Initialize advanced NER pipeline with better models
                try:
                    # Try the most reliable NER model first
                    self.ner_pipeline = pipeline("ner",
                                                model="dbmdz/bert-large-cased-finetuned-conll03-english",
                                                aggregation_strategy="simple")
                    print("✅ Advanced NER pipeline (BERT-large) initialized successfully")
                except Exception as e:
                    print(f"⚠️ BERT-large NER failed: {e}, trying BERT-base NER...")
                    try:
                        # Fallback to BERT-base NER
                        self.ner_pipeline = pipeline("ner",
                                                    model="dbmdz/bert-base-cased-finetuned-conll03-english",
                                                    aggregation_strategy="simple")
                        print("✅ NER pipeline (BERT-base) initialized successfully")
                    except Exception as e:
                        print(f"⚠️ BERT-base NER failed: {e}, trying DistilBERT NER...")
                        try:
                            # Final fallback to DistilBERT NER
                            self.ner_pipeline = pipeline("ner",
                                                        model="distilbert-base-cased",
                                                        aggregation_strategy="simple")
                            print("✅ NER pipeline (DistilBERT) initialized successfully")
                        except Exception as e:
                            print(f"⚠️ All NER models failed: {e}")
                            self.ner_pipeline = None

            except Exception as e:
                print(f"⚠️ BERT/NER initialization failed: {e}")
                self.bert_tokenizer = None
                self.bert_model = None
                self.ner_pipeline = None

        print("🚀 Advanced NLP Service initialization complete!")

    def _initialize_advanced_models(self):
        """Initialize free AI models only"""
        print("🔧 Initializing Free AI Models...")

        # Initialize Free AI Models
        self._initialize_free_models()

        print("🚀 Free AI Models initialization complete!")

    def _initialize_free_models(self):
        """Initialize free AI model clients"""
        print("🔧 Initializing Free AI Models...")

        # Initialize Mistral AI
        if MISTRAL_AVAILABLE:
            try:
                api_key = os.getenv('MISTRAL_API_KEY')
                if api_key:
                    self.mistral_client = Mistral(api_key=api_key)
                    print("✅ Mistral AI client initialized successfully")
                else:
                    print("⚠️ Mistral API key not found. Set MISTRAL_API_KEY environment variable.")
            except Exception as e:
                print(f"⚠️ Mistral initialization failed: {e}")
                self.mistral_client = None

        # Initialize Groq
        if GROQ_AVAILABLE:
            try:
                api_key = os.getenv('GROQ_API_KEY')
                if api_key:
                    self.groq_client = Groq(api_key=api_key)
                    print("✅ Groq client initialized successfully")
                else:
                    print("⚠️ Groq API key not found. Set GROQ_API_KEY environment variable.")
            except Exception as e:
                print(f"⚠️ Groq initialization failed: {e}")
                self.groq_client = None

        # Initialize Together AI
        if TOGETHER_AVAILABLE:
            try:
                api_key = os.getenv('TOGETHER_API_KEY')
                if api_key:
                    together.api_key = api_key
                    self.together_client = together
                    print("✅ Together AI client initialized successfully")
                else:
                    print("⚠️ Together AI API key not found. Set TOGETHER_API_KEY environment variable.")
            except Exception as e:
                print(f"⚠️ Together AI initialization failed: {e}")
                self.together_client = None

        # Initialize Hugging Face
        if HF_AVAILABLE:
            try:
                api_key = os.getenv('HF_API_KEY')
                if api_key:
                    self.hf_api_key = api_key
                    print("✅ Hugging Face API key configured successfully")
                else:
                    print("⚠️ Hugging Face API key not found. Set HF_API_KEY environment variable.")
            except Exception as e:
                print(f"⚠️ Hugging Face initialization failed: {e}")
                self.hf_api_key = None

        print("🚀 Free AI Models initialization complete!")
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """Extract keywords using advanced NLP techniques (TF-IDF, BERT, spaCy)"""
        if not text:
            return []

        # Handle non-string inputs
        if not isinstance(text, str):
            if text is None:
                return []
            text = str(text)

        # Clean text
        text = self._clean_text(text)

        print(f"🔍 Extracting keywords using advanced NLP techniques...")

        # Try TF-IDF first for best results
        if self.tfidf_vectorizer is not None:
            keywords = self._extract_keywords_tfidf(text, max_keywords)
            if keywords:
                print(f"✅ TF-IDF extracted {len(keywords)} keywords")
                return keywords

        # Fallback to spaCy
        if self.nlp:
            keywords = self._extract_keywords_spacy(text, max_keywords)
            print(f"✅ spaCy extracted {len(keywords)} keywords")
            return keywords
        elif NLTK_AVAILABLE:
            keywords = self._extract_keywords_nltk(text, max_keywords)
            print(f"✅ NLTK extracted {len(keywords)} keywords")
            return keywords
        else:
            keywords = self._extract_keywords_simple(text, max_keywords)
            print(f"✅ Simple extraction found {len(keywords)} keywords")
            return keywords
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        # Handle non-string inputs
        if not isinstance(text, str):
            if text is None:
                return ""
            # Convert to string if it's not already
            text = str(text)

        # Remove special characters and normalize whitespace
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip().lower()

    def _extract_keywords_tfidf(self, text: str, max_keywords: int) -> List[str]:
        """Extract keywords using TF-IDF vectorization"""
        try:
            # Clean and prepare text
            if len(text.strip()) < 10:
                print("⚠️ Text too short for TF-IDF analysis")
                return []

            # Create a more flexible TF-IDF vectorizer for this specific text
            from sklearn.feature_extraction.text import TfidfVectorizer

            # Split text into sentences for better TF-IDF analysis
            if NLTK_AVAILABLE:
                sentences = sent_tokenize(text)
            else:
                sentences = [s.strip() for s in text.split('.') if s.strip()]

            # Filter out very short sentences
            sentences = [s for s in sentences if len(s.strip()) > 5]

            # If we have very few sentences, split by other delimiters
            if len(sentences) < 3:
                # Try splitting by other delimiters
                additional_sentences = []
                for delimiter in ['\n', ';', '!', '?']:
                    for sentence in sentences:
                        additional_sentences.extend([s.strip() for s in sentence.split(delimiter) if len(s.strip()) > 5])
                if additional_sentences:
                    sentences = additional_sentences

            # Need at least 2 documents for TF-IDF
            if len(sentences) < 2:
                # Split text into chunks if it's long enough
                if len(text) > 100:
                    chunk_size = max(50, len(text) // 3)
                    sentences = [text[i:i+chunk_size] for i in range(0, len(text), chunk_size)]
                else:
                    # For very short text, create artificial documents
                    words = text.split()
                    if len(words) > 4:
                        mid = len(words) // 2
                        sentences = [' '.join(words[:mid]), ' '.join(words[mid:])]
                    else:
                        sentences = [text, text]  # Last resort

            # Ensure we have valid sentences
            sentences = [s for s in sentences if len(s.strip()) > 3]
            if len(sentences) < 2:
                print("⚠️ Unable to create sufficient documents for TF-IDF, using frequency method")
                return self._extract_keywords_frequency(text, max_keywords)

            # Create a more robust TF-IDF vectorizer with very lenient parameters
            total_words = len(' '.join(sentences).split())
            custom_vectorizer = TfidfVectorizer(
                max_features=min(1000, max(20, total_words)),  # More generous max features
                stop_words=None,  # Don't filter stop words initially
                ngram_range=(1, 2),  # Include bigrams for better context
                min_df=1,  # Very lenient minimum document frequency
                max_df=0.95,  # Allow more common terms
                lowercase=True,
                token_pattern=r'\b[a-zA-Z][a-zA-Z0-9_-]*\b',  # More flexible token pattern
                sublinear_tf=True,  # Use sublinear TF scaling
                smooth_idf=True,  # Smooth IDF weights
                use_idf=True  # Use inverse document frequency
            )

            # Fit TF-IDF on sentences with error handling
            try:
                tfidf_matrix = custom_vectorizer.fit_transform(sentences)
                feature_names = custom_vectorizer.get_feature_names_out()

                if len(feature_names) == 0:
                    print("⚠️ TF-IDF produced empty vocabulary, using frequency method")
                    return self._extract_keywords_frequency(text, max_keywords)

            except ValueError as ve:
                if any(phrase in str(ve).lower() for phrase in ["empty vocabulary", "no terms remain", "after pruning"]):
                    print("⚠️ TF-IDF vocabulary empty, using frequency method")
                    return self._extract_keywords_frequency(text, max_keywords)
                else:
                    raise ve

            # Get average TF-IDF scores across all sentences
            if tfidf_matrix.shape[0] > 1:
                mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)
            else:
                mean_scores = tfidf_matrix.toarray()[0]

            # Get top keywords by TF-IDF score with very low threshold
            top_indices = mean_scores.argsort()[-max_keywords*3:][::-1]  # Get more candidates
            keywords = []

            # Basic English stop words to filter out
            basic_stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'this', 'that', 'with', 'have', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'}

            for i in top_indices:
                if i < len(feature_names) and mean_scores[i] > 0:  # Any positive score
                    keyword = feature_names[i].lower()
                    # Filter out stop words and very short words
                    if (len(keyword) > 2 and
                        not keyword.isdigit() and
                        keyword not in basic_stop_words and
                        keyword.isalpha()):
                        keywords.append(keyword)

            # If we still don't have enough keywords, be even more lenient
            if len(keywords) < max_keywords // 2:
                keywords = []
                for i in top_indices:
                    if i < len(feature_names) and mean_scores[i] > 0:
                        keyword = feature_names[i].lower()
                        if len(keyword) > 1 and not keyword.isdigit():
                            keywords.append(keyword)

            print(f"✅ TF-IDF analysis: processed {len(sentences)} documents, found {len(keywords)} keywords")
            return keywords[:max_keywords] if keywords else self._extract_keywords_frequency(text, max_keywords)

        except ValueError as ve:
            if any(phrase in str(ve).lower() for phrase in ["after pruning", "no terms remain", "empty vocabulary"]):
                print("⚠️ TF-IDF failed: No valid terms after filtering. Falling back to simple word frequency.")
                return self._extract_keywords_frequency(text, max_keywords)
            else:
                print(f"⚠️ TF-IDF extraction failed: {ve}")
                return self._extract_keywords_frequency(text, max_keywords)
        except Exception as e:
            print(f"⚠️ TF-IDF extraction failed: {e}")
            return self._extract_keywords_frequency(text, max_keywords)

    def _extract_keywords_frequency(self, text: str, max_keywords: int) -> List[str]:
        """Fallback keyword extraction using simple word frequency"""
        try:
            import re
            from collections import Counter

            # Clean text and extract words
            words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())

            # Remove common stop words
            stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
            words = [w for w in words if w not in stop_words and len(w) > 2]

            # Count word frequencies
            word_counts = Counter(words)

            # Get most common words
            keywords = [word for word, count in word_counts.most_common(max_keywords)]

            print(f"🔍 Frequency analysis: found {len(keywords)} keywords from {len(words)} words")
            return keywords

        except Exception as e:
            print(f"⚠️ Frequency analysis failed: {e}")
            return []

    def _extract_keywords_spacy(self, text: str, max_keywords: int) -> List[str]:
        """Extract keywords using spaCy"""
        doc = self.nlp(text)
        
        # Extract nouns, proper nouns, and adjectives
        keywords = []
        for token in doc:
            if (token.pos_ in ['NOUN', 'PROPN', 'ADJ'] and 
                not token.is_stop and 
                not token.is_punct and 
                len(token.text) > 2):
                keywords.append(token.lemma_.lower())
        
        # Count frequency and return most common
        keyword_counts = Counter(keywords)
        return [word for word, _ in keyword_counts.most_common(max_keywords)]
    
    def _extract_keywords_nltk(self, text: str, max_keywords: int) -> List[str]:
        """Extract keywords using NLTK"""
        try:
            tokens = word_tokenize(text)
            
            # Filter tokens
            keywords = []
            for token in tokens:
                if (len(token) > 2 and 
                    token.lower() not in self.stop_words and 
                    token.isalpha()):
                    if self.stemmer:
                        keywords.append(self.stemmer.stem(token.lower()))
                    else:
                        keywords.append(token.lower())
            
            # Count frequency and return most common
            keyword_counts = Counter(keywords)
            return [word for word, _ in keyword_counts.most_common(max_keywords)]
        except:
            return self._extract_keywords_simple(text, max_keywords)
    
    def _extract_keywords_simple(self, text: str, max_keywords: int) -> List[str]:
        """Simple keyword extraction without external libraries"""
        # Basic stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
        }
        
        words = text.split()
        keywords = []
        
        for word in words:
            if (len(word) > 2 and 
                word.lower() not in stop_words and 
                word.isalpha()):
                keywords.append(word.lower())
        
        # Count frequency and return most common
        keyword_counts = Counter(keywords)
        return [word for word, _ in keyword_counts.most_common(max_keywords)]
    
    def suggest_tags(self, text: str, num_tags: int = 5) -> List[str]:
        """Suggest tags based on text content"""
        if not isinstance(text, str):
            if text is None:
                return []
            text = str(text)

        keywords = self.extract_keywords(text, num_tags * 2)
        
        # Filter and clean keywords for tags
        tags = []
        for keyword in keywords:
            if len(keyword) > 2 and keyword.isalpha():
                tags.append(keyword.replace('_', ' ').title())
        
        return tags[:num_tags]
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Basic sentiment analysis"""
        if not isinstance(text, str):
            if text is None:
                return {'sentiment': 'neutral', 'confidence': 0.0}
            text = str(text)

        if not text:
            return {'sentiment': 'neutral', 'confidence': 0.0}
        
        # Simple sentiment analysis using word lists
        positive_words = {
            'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 
            'positive', 'beneficial', 'useful', 'valuable', 'important', 'significant'
        }
        
        negative_words = {
            'bad', 'terrible', 'awful', 'horrible', 'negative', 'poor', 'useless', 
            'problematic', 'difficult', 'challenging', 'limited', 'incomplete'
        }
        
        words = self._clean_text(text).split()
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            return {'sentiment': 'neutral', 'confidence': 0.0}
        
        if positive_count > negative_count:
            sentiment = 'positive'
            confidence = positive_count / total_sentiment_words
        elif negative_count > positive_count:
            sentiment = 'negative'
            confidence = negative_count / total_sentiment_words
        else:
            sentiment = 'neutral'
            confidence = 0.5
        
        return {
            'sentiment': sentiment,
            'confidence': round(confidence, 2),
            'positive_words': positive_count,
            'negative_words': negative_count
        }
    
    def extract_entities(self, text: str) -> List[Dict[str, str]]:
        """Extract named entities using advanced NER (BERT-based + spaCy)"""
        if not isinstance(text, str):
            if text is None:
                return []
            text = str(text)

        if not text:
            return []

        print(f"🔍 Extracting entities using advanced NER techniques...")
        entities = []

        # Try BERT-based NER first
        if self.ner_pipeline:
            try:
                bert_entities = self.ner_pipeline(text)
                for ent in bert_entities:
                    entities.append({
                        'text': ent['word'],
                        'label': ent['entity_group'],
                        'confidence': round(ent['score'], 3),
                        'description': self._get_entity_description(ent['entity_group']),
                        'method': 'BERT-NER'
                    })
                print(f"✅ BERT-NER extracted {len(bert_entities)} entities")
            except Exception as e:
                print(f"⚠️ BERT-NER failed: {e}")

        # Also use spaCy NER for comparison
        if self.nlp:
            try:
                doc = self.nlp(text)
                spacy_entities = []
                for ent in doc.ents:
                    spacy_entities.append({
                        'text': ent.text,
                        'label': ent.label_,
                        'confidence': 1.0,  # spaCy doesn't provide confidence scores
                        'description': spacy.explain(ent.label_) if spacy.explain(ent.label_) else ent.label_,
                        'method': 'spaCy-NER'
                    })
                entities.extend(spacy_entities)
                print(f"✅ spaCy-NER extracted {len(spacy_entities)} entities")
            except Exception as e:
                print(f"⚠️ spaCy-NER failed: {e}")

        # Remove duplicates and sort by confidence
        unique_entities = self._deduplicate_entities(entities)
        print(f"🎯 Total unique entities extracted: {len(unique_entities)}")

        return unique_entities

    def _get_entity_description(self, entity_label: str) -> str:
        """Get description for entity labels"""
        descriptions = {
            'PER': 'Person',
            'PERSON': 'Person',
            'ORG': 'Organization',
            'ORGANIZATION': 'Organization',
            'LOC': 'Location',
            'LOCATION': 'Location',
            'MISC': 'Miscellaneous',
            'GPE': 'Geopolitical Entity',
            'DATE': 'Date',
            'TIME': 'Time',
            'MONEY': 'Money',
            'PERCENT': 'Percentage',
            'FACILITY': 'Facility',
            'PRODUCT': 'Product'
        }
        return descriptions.get(entity_label.upper(), entity_label)

    def _deduplicate_entities(self, entities: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Remove duplicate entities and keep highest confidence ones"""
        entity_map = {}

        for entity in entities:
            text = entity['text'].lower().strip()
            if text not in entity_map or entity['confidence'] > entity_map[text]['confidence']:
                entity_map[text] = entity

        # Sort by confidence descending
        return sorted(entity_map.values(), key=lambda x: x['confidence'], reverse=True)
    
    def generate_summary(self, text: str, max_sentences: int = 3) -> str:
        """Generate a simple extractive summary"""
        if not isinstance(text, str):
            if text is None:
                return ""
            text = str(text)

        if not text:
            return ""
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        if len(sentences) <= max_sentences:
            return '. '.join(sentences) + '.'
        
        # Score sentences based on keyword frequency
        keywords = self.extract_keywords(text, 10)
        sentence_scores = {}
        
        for i, sentence in enumerate(sentences):
            score = 0
            sentence_words = self._clean_text(sentence).split()
            for word in sentence_words:
                if word in keywords:
                    score += 1
            sentence_scores[i] = score
        
        # Select top sentences
        top_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)[:max_sentences]
        top_sentences.sort(key=lambda x: x[0])  # Sort by original order
        
        summary_sentences = [sentences[i] for i, _ in top_sentences]
        return '. '.join(summary_sentences) + '.'

    def get_bert_embeddings(self, text: str) -> Optional[np.ndarray]:
        """Get BERT embeddings for text"""
        if not self.bert_model or not self.bert_tokenizer:
            return None

        try:
            # Tokenize and encode
            inputs = self.bert_tokenizer(text, return_tensors="pt", truncation=True, max_length=512)

            # Get embeddings
            with torch.no_grad():
                outputs = self.bert_model(**inputs)
                # Use mean pooling of last hidden states
                embeddings = outputs.last_hidden_state.mean(dim=1).squeeze().numpy()

            print(f"✅ BERT embeddings generated: {embeddings.shape}")
            return embeddings

        except Exception as e:
            print(f"⚠️ BERT embedding generation failed: {e}")
            return None

    def semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity using BERT embeddings"""
        if not self.bert_model:
            return 0.0

        try:
            emb1 = self.get_bert_embeddings(text1)
            emb2 = self.get_bert_embeddings(text2)

            if emb1 is None or emb2 is None:
                return 0.0

            # Calculate cosine similarity
            similarity = cosine_similarity([emb1], [emb2])[0][0]
            print(f"🔍 Semantic similarity calculated: {similarity:.3f}")
            return float(similarity)

        except Exception as e:
            print(f"⚠️ Semantic similarity calculation failed: {e}")
            return 0.0

    def advanced_content_analysis(self, text: str) -> Dict[str, Any]:
        """Perform comprehensive content analysis using all available NLP techniques"""
        if not isinstance(text, str):
            if text is None:
                return {}
            text = str(text)

        print(f"🚀 Starting advanced content analysis...")

        analysis = {
            'text_length': len(text),
            'word_count': len(text.split()),
            'sentence_count': len(sent_tokenize(text)) if NLTK_AVAILABLE else len(text.split('.')),
            'processing_methods': []
        }

        # Extract keywords with multiple methods
        analysis['keywords'] = self.extract_keywords(text, 15)
        analysis['processing_methods'].append('TF-IDF/spaCy/NLTK keyword extraction')

        # Extract entities with advanced NER
        analysis['entities'] = self.extract_entities(text)
        analysis['processing_methods'].append('BERT-NER + spaCy entity recognition')

        # Sentiment analysis
        analysis['sentiment'] = self.analyze_sentiment(text)
        analysis['processing_methods'].append('Lexicon-based sentiment analysis')

        # Generate summary
        analysis['summary'] = self.generate_summary(text, 3)
        analysis['processing_methods'].append('Extractive summarization')

        # BERT embeddings if available
        if self.bert_model:
            embeddings = self.get_bert_embeddings(text)
            if embeddings is not None:
                analysis['bert_embedding_dim'] = embeddings.shape[0]
                analysis['processing_methods'].append('BERT embeddings')

        # TF-IDF analysis if available
        if self.tfidf_vectorizer:
            analysis['processing_methods'].append('TF-IDF vectorization')

        print(f"✅ Advanced content analysis complete: {len(analysis['processing_methods'])} methods used")
        return analysis

    def generate_enhanced_description(self, dataset_info: Dict[str, Any]) -> str:
        """Generate enhanced dataset description using advanced LLM models"""
        print("🔍 Generating enhanced description using advanced AI models...")

        # Try free AI models first (prioritize working models)
        free_models = [
            (self.mistral_client, self._generate_description_mistral, "Mistral AI"),
            (self.groq_client, self._generate_description_groq, "Groq"),
            (self.together_client, self._generate_description_together, "Together AI"),
            (self.hf_api_key, self._generate_description_huggingface, "Hugging Face")
        ]

        for client, method, name in free_models:
            if client:
                try:
                    description = method(dataset_info)
                    if description and len(description) > 100:
                        print(f"✅ Enhanced description generated using {name}")
                        return description
                except Exception as e:
                    print(f"⚠️ {name} description generation failed: {e}")

        # Fallback to comprehensive local NLP description
        print("⚠️ All free AI models unavailable, using advanced local NLP description generation")
        return self._generate_comprehensive_local_description(dataset_info)

# Removed paid API methods - using only free AI models

    def _generate_description_mistral(self, dataset_info: Dict[str, Any]) -> str:
        """Generate description using Mistral AI"""
        try:
            import json

            # Prepare dataset information
            title = dataset_info.get('title', 'Unknown Dataset')
            field_names = dataset_info.get('field_names', [])
            record_count = dataset_info.get('record_count', 0)
            data_types = dataset_info.get('data_types', [])
            sample_data = dataset_info.get('sample_data', [])
            keywords = dataset_info.get('keywords', [])

            # Create comprehensive prompt
            prompt = f"""
            Analyze this dataset and generate a comprehensive, academic description:

            Dataset Information:
            - Title: {title}
            - Number of records: {record_count:,}
            - Number of fields: {len(field_names)}
            - Field names: {', '.join(field_names[:10])}{'...' if len(field_names) > 10 else ''}
            - Data types: {', '.join(set(data_types)) if data_types else 'Mixed'}
            - Keywords: {', '.join(keywords[:10]) if keywords else 'None identified'}

            Sample data (first few records):
            {json.dumps(sample_data[:3], indent=2) if sample_data else 'No sample data available'}

            Please generate a detailed description that includes:
            1. What the dataset contains and its primary purpose
            2. The structure and organization of the data
            3. Potential research applications and use cases
            4. Data quality observations and characteristics
            5. Suggested analysis approaches or methodologies

            Write in an academic tone suitable for research documentation. Be specific and informative while remaining concise.
            Limit the response to approximately 400-600 words.
            """

            # Generate response using Mistral (new API)
            response = self.mistral_client.chat.complete(
                model="mistral-large-latest",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=800
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"⚠️ Mistral API call failed: {e}")
            return ""

    def _generate_description_groq(self, dataset_info: Dict[str, Any]) -> str:
        """Generate description using Groq"""
        try:
            import json

            # Prepare dataset information
            title = dataset_info.get('title', 'Unknown Dataset')
            field_names = dataset_info.get('field_names', [])
            record_count = dataset_info.get('record_count', 0)
            data_types = dataset_info.get('data_types', [])
            sample_data = dataset_info.get('sample_data', [])
            keywords = dataset_info.get('keywords', [])

            # Create comprehensive prompt
            prompt = f"""
            Analyze this dataset and generate a comprehensive, academic description:

            Dataset Information:
            - Title: {title}
            - Number of records: {record_count:,}
            - Number of fields: {len(field_names)}
            - Field names: {', '.join(field_names[:10])}{'...' if len(field_names) > 10 else ''}
            - Data types: {', '.join(set(data_types)) if data_types else 'Mixed'}
            - Keywords: {', '.join(keywords[:10]) if keywords else 'None identified'}

            Sample data (first few records):
            {json.dumps(sample_data[:3], indent=2) if sample_data else 'No sample data available'}

            Please generate a detailed description that includes:
            1. What the dataset contains and its primary purpose
            2. The structure and organization of the data
            3. Potential research applications and use cases
            4. Data quality observations and characteristics
            5. Suggested analysis approaches or methodologies

            Write in an academic tone suitable for research documentation. Be specific and informative while remaining concise.
            """

            # Generate response using Groq
            response = self.groq_client.chat.completions.create(
                model="mixtral-8x7b-32768",  # Fast and high-quality model
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=800
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"⚠️ Groq API call failed: {e}")
            return ""

    def _generate_description_together(self, dataset_info: Dict[str, Any]) -> str:
        """Generate description using Together AI"""
        try:
            import json

            # Prepare dataset information
            title = dataset_info.get('title', 'Unknown Dataset')
            field_names = dataset_info.get('field_names', [])
            record_count = dataset_info.get('record_count', 0)
            data_types = dataset_info.get('data_types', [])
            sample_data = dataset_info.get('sample_data', [])
            keywords = dataset_info.get('keywords', [])

            # Create comprehensive prompt
            prompt = f"""
            Analyze this dataset and generate a comprehensive, academic description:

            Dataset Information:
            - Title: {title}
            - Number of records: {record_count:,}
            - Number of fields: {len(field_names)}
            - Field names: {', '.join(field_names[:10])}{'...' if len(field_names) > 10 else ''}
            - Data types: {', '.join(set(data_types)) if data_types else 'Mixed'}
            - Keywords: {', '.join(keywords[:10]) if keywords else 'None identified'}

            Sample data (first few records):
            {json.dumps(sample_data[:3], indent=2) if sample_data else 'No sample data available'}

            Please generate a detailed description that includes:
            1. What the dataset contains and its primary purpose
            2. The structure and organization of the data
            3. Potential research applications and use cases
            4. Data quality observations and characteristics
            5. Suggested analysis approaches or methodologies

            Write in an academic tone suitable for research documentation. Be specific and informative while remaining concise.
            """

            # Generate response using Together AI
            response = self.together_client.Complete.create(
                prompt=prompt,
                model="meta-llama/Llama-2-70b-chat-hf",
                max_tokens=800,
                temperature=0.3,
                top_p=0.9,
                stop=["</s>"]
            )

            return response['output']['choices'][0]['text'].strip()

        except Exception as e:
            print(f"⚠️ Together AI API call failed: {e}")
            return ""

    def _generate_description_huggingface(self, dataset_info: Dict[str, Any]) -> str:
        """Generate description using Hugging Face Inference API"""
        try:
            import json

            # Prepare dataset information
            title = dataset_info.get('title', 'Unknown Dataset')
            field_names = dataset_info.get('field_names', [])
            record_count = dataset_info.get('record_count', 0)
            data_types = dataset_info.get('data_types', [])
            sample_data = dataset_info.get('sample_data', [])
            keywords = dataset_info.get('keywords', [])

            # Create comprehensive prompt
            prompt = f"""
            Analyze this dataset and generate a comprehensive, academic description:

            Dataset Information:
            - Title: {title}
            - Number of records: {record_count:,}
            - Number of fields: {len(field_names)}
            - Field names: {', '.join(field_names[:10])}{'...' if len(field_names) > 10 else ''}
            - Data types: {', '.join(set(data_types)) if data_types else 'Mixed'}
            - Keywords: {', '.join(keywords[:10]) if keywords else 'None identified'}

            Sample data (first few records):
            {json.dumps(sample_data[:3], indent=2) if sample_data else 'No sample data available'}

            Please generate a detailed description that includes:
            1. What the dataset contains and its primary purpose
            2. The structure and organization of the data
            3. Potential research applications and use cases
            4. Data quality observations and characteristics
            5. Suggested analysis approaches or methodologies

            Write in an academic tone suitable for research documentation. Be specific and informative while remaining concise.
            """

            # Generate response using Hugging Face
            headers = {"Authorization": f"Bearer {self.hf_api_key}"}

            # Try multiple models in order of preference
            models = [
                "microsoft/DialoGPT-large",
                "facebook/blenderbot-400M-distill",
                "microsoft/DialoGPT-medium"
            ]

            for model in models:
                try:
                    response = requests.post(
                        f"https://api-inference.huggingface.co/models/{model}",
                        headers=headers,
                        json={"inputs": prompt, "parameters": {"max_length": 800, "temperature": 0.3}},
                        timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if isinstance(result, list) and len(result) > 0:
                            generated_text = result[0].get('generated_text', '')
                            if generated_text and len(generated_text) > 100:
                                return generated_text.strip()
                except Exception as model_error:
                    print(f"⚠️ HF model {model} failed: {model_error}")
                    continue

            return ""

        except Exception as e:
            print(f"⚠️ Hugging Face API call failed: {e}")
            return ""

    def _generate_comprehensive_local_description(self, dataset_info: Dict[str, Any]) -> str:
        """Generate comprehensive description using advanced local NLP techniques"""
        try:
            title = dataset_info.get('title', 'Dataset')
            field_names = dataset_info.get('field_names', [])
            record_count = dataset_info.get('record_count', 0)
            data_types = dataset_info.get('data_types', [])
            keywords = dataset_info.get('keywords', [])
            category = dataset_info.get('category', '')

            description_parts = []

            # Enhanced introduction with category context
            if category:
                description_parts.append(f"This {category.lower()} dataset, titled '{title}', represents a comprehensive collection of structured data designed for advanced analytical and research applications.")
            else:
                description_parts.append(f"This dataset, titled '{title}', represents a comprehensive collection of structured data designed for advanced analytical and research applications.")

            # Detailed data structure analysis
            if record_count and field_names:
                description_parts.append(f"The dataset encompasses {record_count:,} records systematically organized across {len(field_names)} distinct fields, providing a robust foundation for statistical analysis and data mining operations.")
            elif record_count:
                description_parts.append(f"The dataset contains {record_count:,} records, offering substantial data volume for comprehensive analysis.")

            # Advanced field analysis
            if field_names:
                if len(field_names) <= 8:
                    field_list = ", ".join(field_names)
                    description_parts.append(f"The dataset structure includes the following fields: {field_list}.")
                else:
                    primary_fields = field_names[:6]
                    field_list = ", ".join(primary_fields)
                    description_parts.append(f"Primary fields include: {field_list}, along with {len(field_names) - 6} additional variables providing comprehensive data coverage.")

            # Enhanced data type analysis
            if data_types:
                unique_types = list(set(data_types))
                if 'object' in unique_types or 'string' in unique_types:
                    description_parts.append("The dataset incorporates textual data elements enabling qualitative analysis and natural language processing applications.")

                if any(dtype in ['int64', 'float64', 'numeric'] for dtype in unique_types):
                    description_parts.append("Numerical data components support statistical modeling, mathematical analysis, and quantitative research methodologies.")

            # Advanced keyword and topic analysis
            if keywords:
                if len(keywords) <= 8:
                    keyword_list = ", ".join(keywords)
                    description_parts.append(f"The dataset encompasses key concepts and topics including: {keyword_list}, indicating its relevance for domain-specific research and analysis.")
                else:
                    primary_keywords = keywords[:6]
                    keyword_list = ", ".join(primary_keywords)
                    description_parts.append(f"Primary thematic elements include: {keyword_list}, among {len(keywords) - 6} additional conceptual dimensions.")

            # Comprehensive use case analysis
            use_cases = []

            if record_count and record_count > 1000:
                use_cases.append("large-scale statistical analysis and machine learning model development")
            elif record_count and record_count > 100:
                use_cases.append("statistical analysis and predictive modeling")

            if len(field_names) > 10:
                use_cases.append("multivariate analysis and feature engineering")

            # Default use cases
            if not use_cases:
                use_cases = ["exploratory data analysis", "statistical modeling", "data visualization", "research applications"]

            description_parts.append(f"This dataset is particularly well-suited for {', '.join(use_cases[:-1])}, and {use_cases[-1]}.")

            # Quality and methodology note
            description_parts.append("The structured nature of this dataset, combined with its comprehensive field coverage, makes it a valuable resource for researchers, analysts, and data scientists seeking to derive meaningful insights through rigorous analytical methodologies.")

            return " ".join(description_parts)

        except Exception as e:
            print(f"⚠️ Comprehensive local description generation failed: {e}")
            return self._generate_basic_description(dataset_info)

    def _generate_basic_description(self, dataset_info: Dict[str, Any]) -> str:
        """Generate basic description as fallback"""
        title = dataset_info.get('title', 'Dataset')
        record_count = dataset_info.get('record_count', 0)
        field_count = len(dataset_info.get('field_names', []))
        keywords = dataset_info.get('keywords', [])

        description_parts = [
            f"This dataset, titled '{title}', contains {record_count:,} records with {field_count} fields."
        ]

        if keywords:
            description_parts.append(f"Key topics include: {', '.join(keywords[:5])}.")

        if record_count > 1000:
            description_parts.append("This is a substantial dataset suitable for comprehensive analysis.")
        elif record_count > 100:
            description_parts.append("This dataset provides a good sample size for analysis.")

        description_parts.append("The dataset can be used for research, analysis, and data science applications.")

        return ' '.join(description_parts)

    def analyze_content_basic(self, text_content: str, field_names: List[str] = None) -> Dict[str, Any]:
        """Basic content analysis using existing methods"""
        if not isinstance(text_content, str):
            text_content = str(text_content) if text_content else ""

        return {
            'keywords': self.extract_keywords(text_content, 15),
            'entities': self.extract_entities(text_content),
            'summary': self.generate_summary(text_content),
            'sentiment': self.analyze_sentiment(text_content),
            'tags': self.suggest_tags(text_content, 8)
        }


# Global service instance
nlp_service = NLPService()
